# RAGSearch: Production-Ready Multi-Tenant RAG System

A comprehensive Django-based multi-tenant Retrieval Augmented Generation (RAG) system that ingests, processes, and retrieves information from multiple data sources to enhance LLM responses with relevant, contextual information.

[![Python](https://img.shields.io/badge/Python-3.10%2B-blue)](https://www.python.org/)
[![Django](https://img.shields.io/badge/Django-4.2-green)](https://www.djangoproject.com/)
[![LlamaIndex](https://img.shields.io/badge/LlamaIndex-Latest-orange)](https://www.llamaindex.ai/)
[![Qdrant](https://img.shields.io/badge/Qdrant-Vector%20DB-red)](https://qdrant.tech/)
[![License](https://img.shields.io/badge/License-MIT-yellow)](LICENSE)

## 🌟 Features

### **🏢 Enterprise-Ready Architecture**
- **Multi-Tenant System**: Complete data isolation and security
- **Production-Grade**: Robust error handling, monitoring, and data consistency
- **Scalable Design**: Handles large document collections efficiently

### **🤖 Advanced RAG Capabilities**
- **LlamaIndex Integration**: End-to-end RAG pipeline with native components
- **Hybrid Search**: Vector similarity + BM25 keyword search
- **Smart Query Classification**: Automatic routing to optimal search strategies
- **Enhanced Prompts**: Context-aware response generation with structured output
- **High-Quality Citations**: Clickable references with humanized timestamps

### **📊 Data Sources & Ingestion**
- **Slack Integration**: Team conversations with thread context preservation
- **GitHub Integration**: Repositories, PRs, issues, and discussions
- **Intelligent Chunking**: Content-aware strategies (skip-chunking, token-based, semantic)
- **Data Consistency**: Automatic sync validation between Django and vector databases

### **🔍 Search & Retrieval**
- **Confidence Scoring**: Multi-factor algorithm with quality indicators
- **Professional UI**: Clean, responsive interface with structured responses
- **Real-time Search**: Fast, accurate results with comprehensive details
- **Context Preservation**: Maintains conversation and document relationships

## 🛠️ Technology Stack

### **🔧 Core Framework**
- **Django 4.2+**: Web framework with REST API
- **PostgreSQL 14+**: Primary database with advanced indexing
- **Qdrant**: High-performance vector database

### **🤖 AI & ML**
- **LlamaIndex**: RAG framework and document processing
- **Gemini 1.5 Flash**: Primary LLM for response generation
- **BGE-base-en-v1.5**: Embedding model (768 dimensions)
- **Ollama**: Local LLM fallback (Llama 3)

### **⚙️ Infrastructure**
- **Docker**: Containerized deployment
- **Poetry**: Dependency management
- **Celery**: Async task processing (future)
- **Redis**: Caching and session storage (future)

## 📋 Prerequisites

- Python 3.10+
- PostgreSQL 14+ with pgvector extension
- Qdrant vector database
- Poetry package manager

## 🚀 Quick Start

### **Prerequisites**
- Python 3.10+, PostgreSQL 14+, Docker (for Qdrant)
- Gemini API key (get from [Google AI Studio](https://makersuite.google.com/app/apikey))

### **1. Setup Environment**
```bash
# Clone and setup
git clone <repository-url>
cd RAGSearch

# Install dependencies
poetry install

# Start Qdrant vector database
docker run -p 6333:6333 qdrant/qdrant

# Create PostgreSQL database
createdb multi_source_rag
```

### **2. Configure Environment**
Create `multi_source_rag/.env`:
```bash
# Django settings
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database
DB_NAME=multi_source_rag
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_HOST=localhost
DB_PORT=5432

# Vector Database
QDRANT_HOST=localhost
QDRANT_PORT=6333

# AI Models
GEMINI_API_KEY=your-gemini-api-key
GEMINI_MODEL=gemini-1.5-flash

# Optional: Slack integration
SLACK_API_TOKEN=your-slack-token
```

### **3. Initialize System**
```bash
cd multi_source_rag

# Run migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Start development server
python manage.py runserver
```

### **4. Data Pipeline (Production-Ready)**
```bash
# Complete data pipeline (cleanup → ingest → validate)
python ../scripts/data_pipeline.py

# Or run individual steps
python ../scripts/cleanup_data.py      # Clean databases
python ../scripts/ingest_data.py       # Ingest fresh data
python ../scripts/validate_data_consistency.py  # Validate integrity
```

### Docker Setup

1. **Build and start the Docker containers**:
   ```bash
   docker-compose -f docker/docker-compose.yml up -d
   ```

2. **Initialize the application**:
   ```bash
   ./docker/scripts/init.sh
   ```

## 🔍 Usage

### **🎯 Quick Access**
- **Search Interface**: `http://localhost:8000/search/`
- **Admin Panel**: `http://localhost:8000/admin/`
- **API Documentation**: `http://localhost:8000/api/docs/`

### **📊 Data Management**
1. **Configure Sources**: Use admin panel to set up Slack/GitHub integrations
2. **Run Data Pipeline**: Use production-ready scripts for data management
3. **Monitor Health**: Validation scripts ensure data consistency

### **🔍 Search Features**
- **Natural Language Queries**: Ask questions in plain English
- **Contextual Results**: Get detailed responses with citations
- **Professional UI**: Clean interface with confidence indicators
- **Real-time Search**: Fast, accurate results with comprehensive details

## 🧪 Development

### Code Formatting

```bash
poetry run black .
poetry run isort .
```

### Data Integrity and Management

#### Cleaning All Databases

To start with a clean slate, you can use the database cleaning script:

```bash
# Run with confirmation prompt
python manage.py clean_databases

# Run without confirmation (for automated scripts)
python manage.py clean_databases --confirm
```

This script safely removes all data from both PostgreSQL and Qdrant while preserving the database structure and admin accounts.

## 📚 Documentation

### **📖 Core Documentation**
- [**Setup & Installation**](docs/SETUP.md) - Complete setup guide with troubleshooting
- [**User Guide**](docs/USER_GUIDE.md) - How to use the search interface and features
- [**Admin Guide**](docs/ADMIN_GUIDE.md) - System administration and configuration
- [**API Reference**](docs/API.md) - REST API endpoints and usage examples

### **🔧 Technical Documentation**
- [**Architecture**](docs/ARCHITECTURE.md) - System design and component overview
- [**Development**](docs/DEVELOPMENT.md) - Developer guidelines and contribution guide
- [**Data Pipeline**](docs/DATA_PIPELINE.md) - Ingestion, processing, and validation
- [**Changelog**](docs/CHANGELOG.md) - Version history and improvements

### **📋 Additional Resources**
- [**Scripts Guide**](scripts/README.md) - Production-ready data management scripts
- [**Troubleshooting**](docs/TROUBLESHOOTING.md) - Common issues and solutions

## 📂 Project Structure

```
RAGSearch/
├── multi_source_rag/           # Django application
│   ├── apps/                   # Django applications
│   │   ├── accounts/           # User and tenant management
│   │   ├── api/                # REST API endpoints
│   │   ├── core/               # Core utilities and shared functionality
│   │   ├── documents/          # Document processing and storage
│   │   └── search/             # Search and RAG functionality
│   ├── config/                 # Django settings and configuration
│   ├── static/                 # Static files (CSS, JS, images)
│   ├── templates/              # HTML templates
│   ├── .env                    # Environment configuration
│   └── manage.py               # Django management script
├── docs/                       # Comprehensive documentation
│   ├── SETUP.md                # Setup and installation guide
│   ├── USER_GUIDE.md           # User interface and search guide
│   ├── ADMIN_GUIDE.md          # System administration guide
│   ├── DATA_PIPELINE.md        # Data ingestion and validation
│   ├── ARCHITECTURE.md         # System architecture and design
│   ├── DEVELOPMENT.md          # Developer guidelines
│   ├── API.md                  # REST API reference
│   ├── TROUBLESHOOTING.md      # Common issues and solutions
│   ├── DATA_MODEL.md           # Database schema and relationships
│   └── CHANGELOG.md            # Version history and improvements
├── scripts/                    # Production-ready data management
│   ├── cleanup_data.py         # Safe database cleanup
│   ├── ingest_data.py          # Production-grade data ingestion
│   ├── validate_data_consistency.py  # Data integrity validation
│   ├── data_pipeline.py        # Complete pipeline orchestration
│   └── README.md               # Scripts documentation
└── README.md                   # This file
```

## �� Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgements

- [LlamaIndex](https://github.com/jerryjliu/llama_index) for the RAG framework
- [Qdrant](https://github.com/qdrant/qdrant) for the vector database
- [SentenceTransformers](https://github.com/UKPLab/sentence-transformers) for the embedding models
- [Llama 3](https://ai.meta.com/llama/) for the language model
