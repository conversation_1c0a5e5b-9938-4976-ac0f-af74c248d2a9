#!/usr/bin/env python3
"""
Simple RAG Search Test Script

Tests the RAG search functionality with a basic query.
"""

import os
import sys
import django

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'multi_source_rag'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.search.services.rag_service import RAGService
from apps.accounts.models import Tenant, User

# Get tenant and user
tenant = Tenant.objects.first()
user = User.objects.first()

# Create RAGService instance
rag_service = RAGService(user=user, tenant_slug=tenant.slug)

# Test search
query = "What did <PERSON><PERSON><PERSON> say about product discussions?"
print(f"🔍 Testing search: {query}")
print("-" * 50)

search_result, documents = rag_service.search(query)
print(f"✅ Search completed!")
print(f"📄 Response: {search_result.generated_answer}")
print(f"📚 Citations: {len(search_result.citations.all())} found")
print(f"📊 Documents retrieved: {len(documents)}")
