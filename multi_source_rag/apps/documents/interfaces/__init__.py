from .base import DocumentSourceInterface
from .factory import DocumentSourceFactory
from .file import FileSourceInterface
from .local_slack import LocalSlackSourceInterface
from .slack import SlackSourceInterface

# Import GitHubInterface from github package
from .github import GitHubInterface

__all__ = [
    "DocumentSourceInterface",
    "DocumentSourceFactory",
    "FileSourceInterface",
    "GitHubInterface",
    "LocalSlackSourceInterface",
    "SlackSourceInterface",
]
