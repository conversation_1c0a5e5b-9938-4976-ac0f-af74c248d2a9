"""
GitHub interface for comprehensive data ingestion.

This interface handles all GitHub data sources:
- Pull Requests (with reviews, comments, diffs)
- Issues (with comments, reactions)
- Wiki Pages
- Discussions
- Release Notes
- Project Boards
- Actions/Workflows metadata
"""

import logging
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple, Union

from django.utils import timezone
from github import Github, GithubException, RateLimitExceededException

from ..base import DocumentSourceInterface
from .rate_limit_handler import RateLimitHandler

# Set up logging
logger = logging.getLogger(__name__)


class GitHubInterface(DocumentSourceInterface):
    """
    GitHub interface that handles all GitHub data sources.

    Features:
    - Unified configuration and authentication
    - Comprehensive data extraction from all GitHub sources
    - Modular content type handling
    - Production-ready error handling and rate limiting
    - Clean, maintainable code structure
    """

    # Supported content types
    SUPPORTED_CONTENT_TYPES = [
        "pull_request",
        "issue", 
        "wiki",
        "discussion",
        "release",
        "project_board",
        "workflow"
    ]

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the consolidated GitHub interface.

        Args:
            config: Configuration containing token, owner, repo, and content types
        """
        super().__init__(config)
        self.token = config.get("token")
        self.owner = config.get("owner")
        self.repo = config.get("repo")
        self.content_types = config.get("content_types", self.SUPPORTED_CONTENT_TYPES)
        self.client = None
        self.rate_limit_handler = RateLimitHandler()
        self.stats = {
            "total_documents": 0,
            "by_content_type": {},
            "processing_time": 0,
            "api_calls": 0,
            "rate_limit_hits": 0
        }
        self.initialize_client()

    def initialize_client(self) -> None:
        """Initialize the GitHub client with error handling."""
        if not self.token:
            raise ValueError("GitHub token is required")

        try:
            self.client = Github(self.token, per_page=100)
            # Test the connection
            self.client.get_user().login
            logger.info(f"Successfully initialized GitHub client for {self.owner}/{self.repo}")
        except Exception as e:
            raise ValueError(f"Failed to initialize GitHub client: {str(e)}")

    def validate_config(self) -> bool:
        """Validate the configuration."""
        required_fields = ["token", "owner", "repo"]
        for field in required_fields:
            if not self.config.get(field):
                logger.error(f"Missing required field: {field}")
                return False

        # Validate content types
        invalid_types = set(self.content_types) - set(self.SUPPORTED_CONTENT_TYPES)
        if invalid_types:
            logger.error(f"Invalid content types: {invalid_types}")
            return False

        return True

    def get_source_type(self) -> str:
        """Get the source type."""
        return "github"

    def fetch_documents(self, **kwargs) -> List[Dict[str, Any]]:
        """
        Fetch documents from all configured GitHub sources.

        Args:
            **kwargs: Additional arguments
                - days: Number of days to fetch (default: 30)
                - state: State filter for PRs/issues (default: 'all')
                - include_drafts: Include draft PRs (default: True)
                - max_per_type: Maximum documents per content type (default: 100)

        Returns:
            List of documents from all sources
        """
        start_time = time.time()
        
        # Parse arguments
        days = kwargs.get("days", 30)
        state = kwargs.get("state", "all")
        include_drafts = kwargs.get("include_drafts", True)
        max_per_type = kwargs.get("max_per_type", 100)

        # Calculate since date
        since = datetime.now(timezone.utc) - timedelta(days=days)

        all_documents = []
        
        try:
            repo = self.client.get_repo(f"{self.owner}/{self.repo}")
            
            # Fetch documents by content type
            for content_type in self.content_types:
                try:
                    logger.info(f"Fetching {content_type} documents...")
                    documents = self._fetch_by_content_type(
                        repo, content_type, since, state, include_drafts, max_per_type
                    )
                    all_documents.extend(documents)
                    self.stats["by_content_type"][content_type] = len(documents)
                    logger.info(f"Fetched {len(documents)} {content_type} documents")
                    
                except Exception as e:
                    logger.error(f"Error fetching {content_type} documents: {str(e)}")
                    self.stats["by_content_type"][content_type] = 0

            self.stats["total_documents"] = len(all_documents)
            self.stats["processing_time"] = time.time() - start_time
            
            logger.info(f"Successfully fetched {len(all_documents)} total documents in {self.stats['processing_time']:.2f}s")
            return all_documents

        except RateLimitExceededException:
            self.stats["rate_limit_hits"] += 1
            if self.rate_limit_handler.handle_rate_limit(self.client):
                return self.fetch_documents(**kwargs)
            else:
                raise ValueError("GitHub API rate limit exceeded and could not be handled")

        except GithubException as e:
            logger.error(f"GitHub API error: {str(e)}")
            raise ValueError(f"GitHub API error: {str(e)}")

        except Exception as e:
            logger.error(f"Error fetching documents from GitHub: {str(e)}")
            raise ValueError(f"Error fetching documents from GitHub: {str(e)}")

    def _fetch_by_content_type(
        self,
        repo,
        content_type: str,
        since: datetime,
        state: str,
        include_drafts: bool,
        max_items: int
    ) -> List[Dict[str, Any]]:
        """
        Fetch documents by content type.

        Args:
            repo: GitHub repository object
            content_type: Type of content to fetch
            since: Fetch items updated since this date
            state: State filter
            include_drafts: Include draft items
            max_items: Maximum items to fetch

        Returns:
            List of documents for the content type
        """
        if content_type == "pull_request":
            return self._fetch_pull_requests(repo, since, state, include_drafts, max_items)
        elif content_type == "issue":
            return self._fetch_issues(repo, since, state, max_items)
        elif content_type == "wiki":
            return self._fetch_wiki_pages(repo, since, max_items)
        elif content_type == "discussion":
            return self._fetch_discussions(repo, since, max_items)
        elif content_type == "release":
            return self._fetch_releases(repo, since, max_items)
        elif content_type == "project_board":
            return self._fetch_project_boards(repo, since, max_items)
        elif content_type == "workflow":
            return self._fetch_workflows(repo, since, max_items)
        else:
            logger.warning(f"Unknown content type: {content_type}")
            return []

    def _fetch_pull_requests(
        self, repo, since: datetime, state: str, include_drafts: bool, max_items: int
    ) -> List[Dict[str, Any]]:
        """Fetch pull requests with comprehensive data."""
        documents = []
        count = 0

        try:
            pulls = repo.get_pulls(state=state, sort="updated", direction="desc")

            for pr in pulls:
                if count >= max_items:
                    break

                # Check date filter
                pr_updated_at = pr.updated_at
                if timezone.is_naive(pr_updated_at):
                    pr_updated_at = timezone.make_aware(pr_updated_at)

                if pr_updated_at < since:
                    break

                # Skip drafts if not included
                if not include_drafts and pr.draft:
                    continue

                # Process PR with comprehensive data
                document = self._process_pull_request(pr)
                documents.append(document)
                count += 1

        except Exception as e:
            logger.error(f"Error fetching pull requests: {str(e)}")

        return documents

    def _fetch_issues(self, repo, since: datetime, state: str, max_items: int) -> List[Dict[str, Any]]:
        """Fetch issues (excluding PRs) with comprehensive data."""
        documents = []
        count = 0

        try:
            issues = repo.get_issues(state=state, sort="updated", direction="desc")

            for issue in issues:
                if count >= max_items:
                    break

                # Skip PRs (issues endpoint returns PRs too)
                if hasattr(issue, "pull_request") and issue.pull_request is not None:
                    continue

                # Check date filter
                issue_updated_at = issue.updated_at
                if timezone.is_naive(issue_updated_at):
                    issue_updated_at = timezone.make_aware(issue_updated_at)

                if issue_updated_at < since:
                    break

                # Process issue with comprehensive data
                document = self._process_issue(issue)
                documents.append(document)
                count += 1

        except Exception as e:
            logger.error(f"Error fetching issues: {str(e)}")

        return documents

    def _fetch_wiki_pages(self, repo, since: datetime, max_items: int) -> List[Dict[str, Any]]:
        """Fetch wiki pages."""
        documents = []

        try:
            # Get wiki repository
            wiki_repo_name = f"{self.owner}/{self.repo}.wiki"
            try:
                wiki_repo = self.client.get_repo(wiki_repo_name)
            except GithubException:
                logger.info(f"Wiki repository {wiki_repo_name} not found or not accessible")
                return documents

            # Get wiki contents
            wiki_contents = wiki_repo.get_contents("")
            count = 0

            for content in wiki_contents:
                if count >= max_items:
                    break

                if not content.name.endswith(".md"):
                    continue

                try:
                    # Get last commit for date filtering
                    last_commit = wiki_repo.get_commits(path=content.path)[0]
                    last_updated = last_commit.commit.author.date

                    if last_updated < since:
                        continue

                    # Process wiki page
                    document = self._process_wiki_page(content, last_commit, wiki_repo)
                    documents.append(document)
                    count += 1

                except Exception as e:
                    logger.error(f"Error processing wiki page {content.name}: {str(e)}")

        except Exception as e:
            logger.error(f"Error fetching wiki pages: {str(e)}")

        return documents

    def _fetch_discussions(self, repo, since: datetime, max_items: int) -> List[Dict[str, Any]]:
        """Fetch discussions (placeholder - requires GraphQL)."""
        # Note: GitHub Discussions require GraphQL API
        # This is a placeholder for future implementation
        logger.info("Discussions fetching requires GraphQL implementation (placeholder)")
        return []

    def _fetch_releases(self, repo, since: datetime, max_items: int) -> List[Dict[str, Any]]:
        """Fetch release notes."""
        documents = []
        count = 0

        try:
            releases = repo.get_releases()

            for release in releases:
                if count >= max_items:
                    break

                # Check date filter
                if release.created_at < since:
                    break

                # Process release
                document = self._process_release(release)
                documents.append(document)
                count += 1

        except Exception as e:
            logger.error(f"Error fetching releases: {str(e)}")

        return documents

    def _fetch_project_boards(self, repo, since: datetime, max_items: int) -> List[Dict[str, Any]]:
        """Fetch project boards (placeholder)."""
        # Note: Project boards API is being deprecated in favor of Projects v2
        logger.info("Project boards fetching is placeholder (API deprecated)")
        return []

    def _fetch_workflows(self, repo, since: datetime, max_items: int) -> List[Dict[str, Any]]:
        """Fetch workflow metadata."""
        documents = []
        count = 0

        try:
            workflows = repo.get_workflows()

            for workflow in workflows:
                if count >= max_items:
                    break

                # Check date filter
                if workflow.updated_at < since:
                    continue

                # Process workflow
                document = self._process_workflow(workflow)
                documents.append(document)
                count += 1

        except Exception as e:
            logger.error(f"Error fetching workflows: {str(e)}")

        return documents

    def get_document(self, document_id: str, **kwargs) -> Dict[str, Any]:
        """Get a specific document by ID."""
        # Parse document ID format: type/identifier
        parts = document_id.split("/", 1)
        if len(parts) != 2:
            raise ValueError(f"Invalid document ID format: {document_id}")

        doc_type, identifier = parts

        try:
            repo = self.client.get_repo(f"{self.owner}/{self.repo}")

            if doc_type == "pr":
                pr = repo.get_pull(int(identifier))
                return self._process_pull_request(pr)
            elif doc_type == "issue":
                issue = repo.get_issue(int(identifier))
                return self._process_issue(issue)
            elif doc_type == "release":
                release = repo.get_release(identifier)
                return self._process_release(release)
            else:
                raise ValueError(f"Unsupported document type: {doc_type}")

        except Exception as e:
            logger.error(f"Error getting document {document_id}: {str(e)}")
            raise

    def search_documents(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """Search for documents across all GitHub sources."""
        max_results = kwargs.get("max_results", 50)
        content_types = kwargs.get("content_types", self.content_types)

        documents = []

        try:
            # Use GitHub search API
            search_query = f"{query} repo:{self.owner}/{self.repo}"
            search_results = self.client.search_issues(search_query)

            count = 0
            for item in search_results:
                if count >= max_results:
                    break

                # Determine if it's a PR or issue
                is_pr = hasattr(item, "pull_request") and item.pull_request is not None

                if is_pr and "pull_request" in content_types:
                    repo = self.client.get_repo(f"{self.owner}/{self.repo}")
                    pr = repo.get_pull(item.number)
                    document = self._process_pull_request(pr)
                    documents.append(document)
                    count += 1
                elif not is_pr and "issue" in content_types:
                    document = self._process_issue(item)
                    documents.append(document)
                    count += 1

        except Exception as e:
            logger.error(f"Error searching documents: {str(e)}")

        return documents

    def _process_pull_request(self, pr) -> Dict[str, Any]:
        """Process a pull request into a document."""
        try:
            # Extract comprehensive PR data
            content_parts = [
                f"# Pull Request #{pr.number}: {pr.title}",
                f"**Author:** {pr.user.login}",
                f"**State:** {pr.state}",
                f"**Created:** {pr.created_at}",
                f"**Updated:** {pr.updated_at}",
                ""
            ]

            if pr.body:
                content_parts.extend(["## Description", pr.body, ""])

            # Add review comments
            try:
                review_comments = list(pr.get_review_comments())
                if review_comments:
                    content_parts.append("## Review Comments")
                    for comment in review_comments[:10]:  # Limit to avoid huge documents
                        content_parts.append(f"**{comment.user.login}:** {comment.body}")
                    content_parts.append("")
            except Exception as e:
                logger.warning(f"Error fetching review comments for PR {pr.number}: {e}")

            # Add regular comments
            try:
                comments = list(pr.get_issue_comments())
                if comments:
                    content_parts.append("## Comments")
                    for comment in comments[:10]:  # Limit to avoid huge documents
                        content_parts.append(f"**{comment.user.login}:** {comment.body}")
                    content_parts.append("")
            except Exception as e:
                logger.warning(f"Error fetching comments for PR {pr.number}: {e}")

            # Add file changes summary
            try:
                files = list(pr.get_files())
                if files:
                    content_parts.append("## Files Changed")
                    for file in files[:20]:  # Limit to avoid huge documents
                        content_parts.append(f"- {file.filename} (+{file.additions}/-{file.deletions})")
                    content_parts.append("")
            except Exception as e:
                logger.warning(f"Error fetching files for PR {pr.number}: {e}")

            return {
                "id": f"pr/{pr.number}",
                "external_id": str(pr.number),
                "title": pr.title,
                "content": "\n".join(content_parts),
                "content_type": "github_pr",
                "metadata": {
                    "source_type": "github",
                    "content_type": "pull_request",
                    "repo": f"{self.owner}/{self.repo}",
                    "number": pr.number,
                    "state": pr.state,
                    "author": pr.user.login,
                    "created_at": pr.created_at.isoformat() if pr.created_at else None,
                    "updated_at": pr.updated_at.isoformat() if pr.updated_at else None,
                    "merged": pr.merged,
                    "draft": pr.draft,
                    "additions": pr.additions,
                    "deletions": pr.deletions,
                    "changed_files": pr.changed_files,
                    "html_url": pr.html_url,
                },
                "created_at": pr.created_at,
                "updated_at": pr.updated_at,
                "url": pr.html_url
            }
        except Exception as e:
            logger.error(f"Error processing pull request {pr.number}: {str(e)}")
            raise

    def _process_issue(self, issue) -> Dict[str, Any]:
        """Process an issue into a document."""
        try:
            # Extract comprehensive issue data
            content_parts = [
                f"# Issue #{issue.number}: {issue.title}",
                f"**Author:** {issue.user.login}",
                f"**State:** {issue.state}",
                f"**Created:** {issue.created_at}",
                f"**Updated:** {issue.updated_at}",
                ""
            ]

            if issue.body:
                content_parts.extend(["## Description", issue.body, ""])

            # Add labels
            if issue.labels:
                labels = [label.name for label in issue.labels]
                content_parts.extend(["## Labels", ", ".join(labels), ""])

            # Add comments
            try:
                comments = list(issue.get_comments())
                if comments:
                    content_parts.append("## Comments")
                    for comment in comments[:10]:  # Limit to avoid huge documents
                        content_parts.append(f"**{comment.user.login}:** {comment.body}")
                    content_parts.append("")
            except Exception as e:
                logger.warning(f"Error fetching comments for issue {issue.number}: {e}")

            return {
                "id": f"issue/{issue.number}",
                "external_id": str(issue.number),
                "title": issue.title,
                "content": "\n".join(content_parts),
                "content_type": "github_issue",
                "metadata": {
                    "source_type": "github",
                    "content_type": "issue",
                    "repo": f"{self.owner}/{self.repo}",
                    "number": issue.number,
                    "state": issue.state,
                    "author": issue.user.login,
                    "created_at": issue.created_at.isoformat() if issue.created_at else None,
                    "updated_at": issue.updated_at.isoformat() if issue.updated_at else None,
                    "labels": [label.name for label in issue.labels],
                    "html_url": issue.html_url,
                },
                "created_at": issue.created_at,
                "updated_at": issue.updated_at,
                "url": issue.html_url
            }
        except Exception as e:
            logger.error(f"Error processing issue {issue.number}: {str(e)}")
            raise

    def _process_wiki_page(self, content, last_commit, wiki_repo) -> Dict[str, Any]:
        """Process a wiki page into a document."""
        try:
            import base64

            # Get file content
            file_content = base64.b64decode(content.content).decode("utf-8")

            # Extract title from filename
            page_name = content.name.replace(".md", "")
            title = page_name.replace("-", " ").replace("_", " ")

            # Try to extract a better title from content
            import re
            heading_match = re.search(r"^#\s+(.+)$", file_content, re.MULTILINE)
            if heading_match:
                title = heading_match.group(1)

            return {
                "id": f"wiki/{content.path}",
                "external_id": content.path,
                "title": title,
                "content": file_content,
                "content_type": "markdown",
                "metadata": {
                    "source_type": "github",
                    "content_type": "wiki",
                    "repo": f"{self.owner}/{self.repo}",
                    "path": content.path,
                    "sha": content.sha,
                    "size": content.size,
                    "last_updated": last_commit.commit.author.date.isoformat(),
                    "last_updated_by": {
                        "name": last_commit.commit.author.name,
                        "email": last_commit.commit.author.email,
                    },
                    "commit_message": last_commit.commit.message,
                    "html_url": f"https://github.com/{self.owner}/{self.repo}/wiki/{page_name}",
                },
                "created_at": last_commit.commit.author.date,
                "updated_at": last_commit.commit.author.date,
                "url": f"https://github.com/{self.owner}/{self.repo}/wiki/{page_name}"
            }
        except Exception as e:
            logger.error(f"Error processing wiki page {content.name}: {str(e)}")
            raise

    def _process_release(self, release) -> Dict[str, Any]:
        """Process a release into a document."""
        try:
            content_parts = [
                f"# Release {release.tag_name}: {release.name or release.tag_name}",
                f"**Published:** {release.published_at}",
                f"**Author:** {release.author.login if release.author else 'Unknown'}",
                ""
            ]

            if release.body:
                content_parts.extend(["## Release Notes", release.body, ""])

            # Add assets information
            if release.get_assets():
                content_parts.append("## Assets")
                for asset in release.get_assets():
                    content_parts.append(f"- {asset.name} ({asset.size} bytes, {asset.download_count} downloads)")
                content_parts.append("")

            return {
                "id": f"release/{release.tag_name}",
                "external_id": release.tag_name,
                "title": release.name or release.tag_name,
                "content": "\n".join(content_parts),
                "content_type": "markdown",
                "metadata": {
                    "source_type": "github",
                    "content_type": "release",
                    "repo": f"{self.owner}/{self.repo}",
                    "tag_name": release.tag_name,
                    "target_commitish": release.target_commitish,
                    "draft": release.draft,
                    "prerelease": release.prerelease,
                    "created_at": release.created_at.isoformat() if release.created_at else None,
                    "published_at": release.published_at.isoformat() if release.published_at else None,
                    "author": release.author.login if release.author else None,
                    "html_url": release.html_url,
                },
                "created_at": release.created_at,
                "updated_at": release.published_at,
                "url": release.html_url
            }
        except Exception as e:
            logger.error(f"Error processing release {release.tag_name}: {str(e)}")
            raise

    def _process_workflow(self, workflow) -> Dict[str, Any]:
        """Process a workflow into a document."""
        try:
            content_parts = [
                f"# Workflow: {workflow.name}",
                f"**Path:** {workflow.path}",
                f"**State:** {workflow.state}",
                f"**Created:** {workflow.created_at}",
                f"**Updated:** {workflow.updated_at}",
                ""
            ]

            # Add workflow runs summary (recent runs)
            try:
                runs = list(workflow.get_runs()[:5])  # Get last 5 runs
                if runs:
                    content_parts.append("## Recent Runs")
                    for run in runs:
                        status = run.status
                        conclusion = run.conclusion or "in_progress"
                        content_parts.append(f"- Run #{run.run_number}: {status} ({conclusion}) - {run.created_at}")
                    content_parts.append("")
            except Exception as e:
                logger.warning(f"Error fetching workflow runs for {workflow.name}: {e}")

            return {
                "id": f"workflow/{workflow.id}",
                "external_id": str(workflow.id),
                "title": workflow.name,
                "content": "\n".join(content_parts),
                "content_type": "text",
                "metadata": {
                    "source_type": "github",
                    "content_type": "workflow",
                    "repo": f"{self.owner}/{self.repo}",
                    "workflow_id": workflow.id,
                    "path": workflow.path,
                    "state": workflow.state,
                    "created_at": workflow.created_at.isoformat() if workflow.created_at else None,
                    "updated_at": workflow.updated_at.isoformat() if workflow.updated_at else None,
                    "html_url": workflow.html_url,
                },
                "created_at": workflow.created_at,
                "updated_at": workflow.updated_at,
                "url": workflow.html_url
            }
        except Exception as e:
            logger.error(f"Error processing workflow {workflow.name}: {str(e)}")
            raise

    def get_stats(self) -> Dict[str, Any]:
        """Get processing statistics."""
        return self.stats.copy()
