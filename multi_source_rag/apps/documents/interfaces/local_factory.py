"""
Factory for document source interfaces with local data support.

This factory extends the standard DocumentSourceFactory to include support for local data sources.
"""

from typing import Any, Dict, Optional

from .base import DocumentSourceInterface
from .file import FileSourceInterface
from .local_slack import LocalSlackSourceInterface


class LocalDocumentSourceFactory:
    """
    Factory for creating document source interfaces with local data support.

    This factory extends the standard DocumentSourceFactory to include support for local data sources.
    It follows the same interface as the standard factory to ensure compatibility.
    """

    @staticmethod
    def create_source(
        source_type: str, config: Optional[Dict[str, Any]] = None
    ) -> DocumentSourceInterface:
        """
        Create a document source interface.

        Args:
            source_type: Type of source to create
            config: Configuration for the source

        Returns:
            DocumentSourceInterface: Document source interface

        Raises:
            ValueError: If the source type is not supported
        """
        config = config or {}

        if source_type == "file":
            return FileSourceInterface(config)
        elif source_type == "slack":
            # Use local Slack interface instead of the standard one
            return LocalSlackSourceInterface(config)
        elif source_type == "local_slack":
            # Use local Slack interface for local_slack source type
            return LocalSlackSourceInterface(config)
        elif source_type == "github":
            from .github import GitHubInterface
            return GitHubInterface(config)
        else:
            raise ValueError(f"Unsupported source type: {source_type}")
